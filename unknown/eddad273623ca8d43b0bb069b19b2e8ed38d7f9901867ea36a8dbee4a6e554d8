@import '../global';

.mainContainer {
    display: flex;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;

    // Left Pane - Reports List
    .leftPane {
        width: 300px;
        min-width: 300px;
        display: flex;
        flex-direction: column;

        .leftPaneHeader {
            padding: 20px;
            border-bottom: 1px solid $lightGrey;
            background-color: $primaryColor;
            color: $white;
            font-weight: bold;
            font-size: 16px;
        }

        .reportsList {
            flex: 1;
            overflow-y: auto;
            padding: 10px;

            .reportItem {
                padding: 15px;
                margin-bottom: 8px;
                background-color: $offWhite;
                border: 1px solid $lightGrey;
                border-radius: $tinyBorderRadius;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    background-color: $grey;
                    border-color: $primaryColor;
                }

                &.selected {
                    background-color: $primaryColor;
                    border-color: $primaryColor;
                    color: $primaryColor;
                    font-weight: bold;
                }

                .reportName {
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 4px;
                }

                .reportClient {
                    font-size: 12px;
                    color: $darkGrey;
                }
            }

            .noReports {
                text-align: center;
                color: $darkGrey;
                padding: 40px 20px;
                font-style: italic;
            }
        }
    }

    // Right Pane - Report Form
    .rightPane {
        flex: 1;
        display: flex;
        flex-direction: column;

        .rightPaneHeader {
            padding: 20px;
            border-bottom: 1px solid $lightGrey;
            background-color: $secondaryColor;
            color: $white;
            font-weight: bold;
            font-size: 16px;
        }

        .formContainer {
            flex: 1;
            padding: 30px;
            overflow-y: auto;

            .formTitle {
                font-size: 18px;
                font-weight: bold;
                color: $primaryColor;
                margin-bottom: 20px;
            }
        }

        .noSelection {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            color: $darkGrey;
            font-style: italic;
            font-size: 16px;
        }
    }
}

// Responsive Design
@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        flex-direction: column;
        height: auto;

        .leftPane {
            width: 100%;
            min-width: auto;
            max-height: 300px;
        }

        .rightPane {
            min-height: 500px;
        }
    }
}
