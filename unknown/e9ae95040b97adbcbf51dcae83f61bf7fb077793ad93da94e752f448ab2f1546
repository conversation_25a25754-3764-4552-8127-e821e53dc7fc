"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { PropertyModel } from "../../../models/Property";
import J<PERSON>_Spinner from "../../../components/JC_Spinner/JC_Spinner";

export default function DefectsEditPage() {
    const params = useParams();
    const propertyId = params.id as string;

    // - STATE - //
    const [property, setProperty] = useState<PropertyModel | null>(null);
    const [initialised, setInitialised] = useState<boolean>(false);

    // - LOAD DATA - //
    async function loadData() {
        try {
            const propertyData = await PropertyModel.Get(propertyId);
            setProperty(propertyData);
        } catch (error) {
            console.error('Error loading property:', error);
        } finally {
            setInitialised(true);
        }
    }

    // Load data on mount
    useEffect(() => {
        if (propertyId) {
            loadData();
        }
    }, [propertyId]);

    // - RENDER - //
    if (!initialised) {
        return <JC_Spinner isPageBody />;
    }

    return (
        <div className={styles.mainContainer}>
            {/* Header */}
            <div className={styles.header}>
                <h2 className={styles.headerLabel}>
                    Defects - {property?.Address || 'Loading...'}
                </h2>
            </div>

            <div className={styles.contentContainer}>
                {/* Left Panel - Rooms */}
                <div className={styles.leftPanel}>
                    <div className={styles.roomsHeader}>Rooms</div>
                    <div className={styles.roomsList}>
                        <div className={styles.comingSoon}>
                            Coming soon...
                        </div>
                    </div>
                </div>

                {/* Right Panel - Defects */}
                <div className={styles.rightPanel}>
                    <div className={styles.comingSoon}>
                        Coming soon...
                    </div>
                </div>
            </div>
        </div>
    );
}
